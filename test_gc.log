2023-09-14T15:34:53.212+0800: 3431.013: Application time: 0.0984141 seconds
{Heap before GC invocations=230 (full 1):
 par new generation   total 1132416K, used 1047455K [0x0000000660800000, 0x00000006ad4c0000, 0x00000006ad4c0000)
  eden space 1006592K, 100% used [0x0000000660800000, 0x000000069df00000, 0x000000069df00000)
  from space 125824K,  32% used [0x000000069df00000, 0x00000006a06e7d70, 0x00000006a59e0000)
  to   space 125824K,   0% used [0x00000006a59e0000, 0x00000006a59e0000, 0x00000006ad4c0000)
 concurrent mark-sweep generation total 5033216K, used 2530913K [0x00000006ad4c0000, 0x00000007e0800000, 0x00000007e0800000)
 Metaspace       used 116362K, capacity 119557K, committed 119936K, reserved 1157120K
  class space    used 11316K, capacity 12039K, committed 12160K, reserved 1048576K
}
2023-09-14T15:34:53.213+0800: 3431.014: #231: [GC (Allocation Failure) 2023-09-14T15:34:53.213+0800: 3431.014: #231: [ParNew
Desired survivor size 64421888 bytes, new threshold 1 (max 15)
- age   1:   16743024 bytes,   16743024 total
: 1047455K->24599K(1132416K), 0.0137047 secs] 3578368K->2555940K(6165632K), 0.0138026 secs] [Times: user=0.10 sys=0.00, real=0.01 secs] 
Heap after GC invocations=231 (full 1):
 par new generation   total 1132416K, used 24599K [0x0000000660800000, 0x00000006ad4c0000, 0x00000006ad4c0000)
  eden space 1006592K,   0% used [0x0000000660800000, 0x0000000660800000, 0x000000069df00000)
  from space 125824K,  19% used [0x00000006a59e0000, 0x00000006a71e5e18, 0x00000006ad4c0000)
  to   space 125824K,   0% used [0x000000069df00000, 0x000000069df00000, 0x00000006a59e0000)
 concurrent mark-sweep generation total 5033216K, used 2531341K [0x00000006ad4c0000, 0x00000007e0800000, 0x00000007e0800000)
 Metaspace       used 116362K, capacity 119557K, committed 119936K, reserved 1157120K
  class space    used 11316K, capacity 12039K, committed 12160K, reserved 1048576K
}
2023-09-14T16:13:09.893+0800: 5728.695: Application time: 1.1508084 seconds
{Heap before GC invocations=726 (full 2):
 par new generation   total 1132416K, used 1023634K [0x0000000660800000, 0x00000006ad4c0000, 0x00000006ad4c0000)
  eden space 1006592K, 100% used [0x0000000660800000, 0x000000069df00000, 0x000000069df00000)
  from space 125824K,  13% used [0x000000069df00000, 0x000000069efa4b38, 0x00000006a59e0000)
  to   space 125824K,   0% used [0x00000006a59e0000, 0x00000006a59e0000, 0x00000006ad4c0000)
 concurrent mark-sweep generation total 5033216K, used 2254256K [0x00000006ad4c0000, 0x00000007e0800000, 0x00000007e0800000)
 Metaspace       used 121778K, capacity 125135K, committed 125440K, reserved 1163264K
  class space    used 11696K, capacity 12456K, committed 12544K, reserved 1048576K
}
2023-09-14T16:13:10.902+0800: 5728.703: #727: [GC (Allocation Failure) 2023-09-14T16:13:10.902+0800: 5728.703: #727: [ParNew
Desired survivor size 64421888 bytes, new threshold 1 (max 15)
- age   1:   10167840 bytes,   10167840 total
: 1023634K->12457K(1132416K), 0.0089652 secs] 3277890K->2266715K(6165632K), 0.0090528 secs] [Times: user=0.06 sys=0.00, real=0.01 secs] 
Heap after GC invocations=727 (full 2):
 par new generation   total 1132416K, used 12457K [0x0000000660800000, 0x00000006ad4c0000, 0x00000006ad4c0000)
  eden space 1006592K,   0% used [0x0000000660800000, 0x0000000660800000, 0x000000069df00000)
  from space 125824K,   9% used [0x00000006a59e0000, 0x00000006a660a630, 0x00000006ad4c0000)
  to   space 125824K,   0% used [0x000000069df00000, 0x000000069df00000, 0x00000006a59e0000)
 concurrent mark-sweep generation total 5033216K, used 2254258K [0x00000006ad4c0000, 0x00000007e0800000, 0x00000007e0800000)
 Metaspace       used 121778K, capacity 125135K, committed 125440K, reserved 1163264K
  class space    used 11696K, capacity 12456K, committed 12544K, reserved 1048576K
}
2023-09-14T16:13:10.903+0800: 5728.704: Total time for which application threads were stopped: 0.0095015 seconds, Stopping threads took: 0.0001453 seconds
2023-09-14T16:13:12.733+0800: 5730.534: Application time: 1.8296671 seconds
2023-09-14T16:13:12.814+0800: 5730.616: Total time for which application threads were stopped: 0.6002985 seconds, Stopping threads took: 0.0001107 seconds
