#!/bin/bash

# 调试CMS Full GC检测
echo "调试CMS Full GC检测..."
echo "========================"

# 简单的检测逻辑
awk '
BEGIN { 
    prev_full = -1
    print "开始分析..."
}

/Heap before GC invocations=.*\(full [0-9]+\)/ {
    match($0, /\(full ([0-9]+)\)/, arr)
    current_full = arr[1]
    
    match($0, /([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+)/, time_arr)
    timestamp = time_arr[1]
    
    print "发现 Heap before: full=" current_full ", prev_full=" prev_full ", time=" timestamp
    
    if (prev_full != -1 && current_full > prev_full) {
        print "检测到Full GC! 从 " prev_full " 增加到 " current_full
        print "时间: " timestamp
        print "原始行: " $0
        print "---"
    }
    prev_full = current_full
}

/Heap after GC invocations=.*\(full [0-9]+\)/ {
    match($0, /\(full ([0-9]+)\)/, arr)
    current_full = arr[1]
    print "发现 Heap after: full=" current_full
}

END {
    print "分析完成"
}
' test_gc.log
