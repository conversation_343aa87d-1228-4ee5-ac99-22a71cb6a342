#!/bin/bash

# 改进的Java GC日志分析脚本
# 适用于CMS垃圾收集器的详细日志格式

if [ $# -eq 0 ]; then
    echo "用法: $0 <GC日志文件路径>"
    echo "示例: $0 gc.log"
    exit 1
fi

GC_LOG_FILE="$1"

if [ ! -f "$GC_LOG_FILE" ]; then
    echo "错误: 文件 '$GC_LOG_FILE' 不存在"
    exit 1
fi

echo "========================================="
echo "Java GC日志分析报告 (CMS收集器)"
echo "分析文件: $GC_LOG_FILE"
echo "分析时间: $(date)"
echo "========================================="

# 1. 分析老年代回收（Full GC和CMS相关）
echo
echo "1. 老年代回收分析"
echo "----------------------------------------"

# 查找Full GC事件
echo "=== Full GC事件 ==="
grep -E "Full GC|concurrent mark-sweep generation" "$GC_LOG_FILE" | while read line; do
    # 提取时间戳
    timestamp=$(echo "$line" | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+')
    if [ -z "$timestamp" ]; then
        timestamp=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//')
    fi
    
    # 提取内存信息 (格式: xxxK->yyyK(zzzK))
    memory_info=$(echo "$line" | grep -oE '[0-9]+[KMG]?->[0-9]+[KMG]?\([0-9]+[KMG]?\)')
    
    # 提取停顿时间
    pause_time=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+[ ]*secs?' | tail -1)
    
    if [ ! -z "$timestamp" ]; then
        echo "时间: $timestamp | 内存变化: $memory_info | 停顿时间: $pause_time"
    fi
done

# 查找CMS相关的老年代回收
echo
echo "=== CMS老年代回收事件 ==="
grep -E "CMS|concurrent mark-sweep" "$GC_LOG_FILE" | grep -v "Full GC" | while read line; do
    timestamp=$(echo "$line" | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+')
    if [ -z "$timestamp" ]; then
        timestamp=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//')
    fi
    
    # CMS阶段信息
    cms_phase=$(echo "$line" | grep -oE 'CMS[^,]*' | head -1)
    pause_time=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+[ ]*secs?' | tail -1)
    
    if [ ! -z "$timestamp" ] && [ ! -z "$cms_phase" ]; then
        echo "时间: $timestamp | CMS阶段: $cms_phase | 停顿时间: $pause_time"
    fi
done

# 2. 分析Stop-The-World超过500ms的情况
echo
echo "2. Stop-The-World超过500ms的时间点"
echo "----------------------------------------"

# 查找应用程序停顿时间超过0.5秒的事件
grep "Total time for which application threads were stopped" "$GC_LOG_FILE" | while read line; do
    # 提取停顿时间
    pause_time=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+[ ]*seconds' | grep -oE '[0-9]+\.[0-9]+')
    
    if [ ! -z "$pause_time" ]; then
        # 检查是否超过0.5秒
        is_long_pause=$(echo "$pause_time" | awk '{if($1 > 0.5) print "yes"; else print "no"}')
        
        if [ "$is_long_pause" = "yes" ]; then
            # 提取时间戳
            timestamp=$(echo "$line" | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+')
            if [ -z "$timestamp" ]; then
                timestamp=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//')
            fi
            
            echo "时间: $timestamp | 停顿时长: ${pause_time}秒"
        fi
    fi
done

# 3. 统计Young GC的频率
echo
echo "3. Young GC频率统计"
echo "----------------------------------------"

# 提取ParNew (Young GC)事件
young_gc_lines=$(grep -E "ParNew|Young" "$GC_LOG_FILE")
young_gc_count=$(echo "$young_gc_lines" | grep -c "ParNew\|Young")

if [ $young_gc_count -gt 0 ]; then
    echo "Young GC总次数: $young_gc_count"
    
    # 提取第一个和最后一个Young GC的时间戳
    first_line=$(echo "$young_gc_lines" | head -1)
    last_line=$(echo "$young_gc_lines" | tail -1)
    
    first_timestamp=$(echo "$first_line" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//' | head -1)
    last_timestamp=$(echo "$last_line" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//' | head -1)
    
    if [ ! -z "$first_timestamp" ] && [ ! -z "$last_timestamp" ]; then
        # 计算总时间跨度
        total_time=$(echo "$last_timestamp $first_timestamp" | awk '{print $1 - $2}')
        
        if [ $(echo "$total_time > 0" | bc -l 2>/dev/null || echo "0") -eq 1 ]; then
            # 计算平均频率
            avg_frequency=$(echo "$young_gc_count $total_time" | awk '{printf "%.4f", $1/$2}')
            echo "时间跨度: ${total_time}秒"
            echo "平均频率: ${avg_frequency}次/秒"
            
            # 分析最高频率（1分钟窗口）
            echo
            echo "分析1分钟窗口内的最高频率..."
            
            # 创建临时文件存储时间戳
            temp_file=$(mktemp)
            echo "$young_gc_lines" | grep -oE '[0-9]+\.[0-9]+:' | sed 's/:$//' > "$temp_file"
            
            max_frequency=0
            max_frequency_time=""
            
            # 滑动窗口分析
            while read timestamp; do
                if [ ! -z "$timestamp" ]; then
                    window_end=$(echo "$timestamp + 60" | bc -l 2>/dev/null || echo "$timestamp")
                    window_count=$(awk -v start="$timestamp" -v end="$window_end" '$1 >= start && $1 <= end {count++} END {print count+0}' "$temp_file")
                    
                    if [ $window_count -gt $max_frequency ]; then
                        max_frequency=$window_count
                        max_frequency_time=$timestamp
                    fi
                fi
            done < "$temp_file"
            
            rm -f "$temp_file"
            
            echo "1分钟内最高频率: ${max_frequency}次 (时间点: ${max_frequency_time}秒)"
        fi
    fi
else
    echo "未找到Young GC事件"
fi

# 4. 详细的GC统计分析
echo
echo "4. 详细GC统计"
echo "----------------------------------------"

# 统计各种GC事件
total_gc_events=$(grep -cE "#[0-9]+:" "$GC_LOG_FILE")
full_gc_count=$(grep -cE "Full GC" "$GC_LOG_FILE")
young_gc_count=$(grep -cE "ParNew" "$GC_LOG_FILE")
cms_concurrent_count=$(grep -cE "CMS-concurrent" "$GC_LOG_FILE")

echo "总GC事件数: $total_gc_events"
echo "Young GC次数: $young_gc_count"
echo "Full GC次数: $full_gc_count"
echo "CMS并发阶段次数: $cms_concurrent_count"

# 分析平均停顿时间
echo
echo "=== 平均停顿时间分析 ==="

# Young GC平均停顿时间
young_gc_times=$(grep "ParNew" "$GC_LOG_FILE" | grep -oE '[0-9]+\.[0-9]+[ ]*secs?' | grep -oE '[0-9]+\.[0-9]+')
if [ ! -z "$young_gc_times" ]; then
    avg_young_pause=$(echo "$young_gc_times" | awk '{sum+=$1; count++} END {if(count>0) printf "%.4f", sum/count; else print "0"}')
    max_young_pause=$(echo "$young_gc_times" | awk 'BEGIN{max=0} {if($1>max) max=$1} END {print max}')
    echo "Young GC平均停顿时间: ${avg_young_pause}秒"
    echo "Young GC最大停顿时间: ${max_young_pause}秒"
fi

# Full GC平均停顿时间
full_gc_times=$(grep "Full GC" "$GC_LOG_FILE" | grep -oE '[0-9]+\.[0-9]+[ ]*secs?' | grep -oE '[0-9]+\.[0-9]+')
if [ ! -z "$full_gc_times" ]; then
    avg_full_pause=$(echo "$full_gc_times" | awk '{sum+=$1; count++} END {if(count>0) printf "%.4f", sum/count; else print "0"}')
    max_full_pause=$(echo "$full_gc_times" | awk 'BEGIN{max=0} {if($1>max) max=$1} END {print max}')
    echo "Full GC平均停顿时间: ${avg_full_pause}秒"
    echo "Full GC最大停顿时间: ${max_full_pause}秒"
fi

# 5. 内存使用分析
echo
echo "5. 内存使用趋势分析"
echo "----------------------------------------"

# 分析堆内存使用情况
echo "=== 堆内存使用分析 ==="
grep -E "Heap after GC" -A 10 "$GC_LOG_FILE" | grep -E "eden|from|to|concurrent mark-sweep generation" | head -10

echo
echo "========================================="
echo "分析完成"
echo "========================================="