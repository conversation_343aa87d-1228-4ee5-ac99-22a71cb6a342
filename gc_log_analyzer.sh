#!/bin/bash

# Java GC日志分析脚本
# 用途：分析GC日志中的老年代回收、长时间停顿和Young GC频率

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <GC日志文件路径>"
    echo "示例: $0 gc.log"
    exit 1
fi

GC_LOG_FILE="$1"

# 检查文件是否存在
if [ ! -f "$GC_LOG_FILE" ]; then
    echo "错误: 文件 '$GC_LOG_FILE' 不存在"
    exit 1
fi

echo "========================================="
echo "Java GC日志分析报告"
echo "分析文件: $GC_LOG_FILE"
echo "分析时间: $(date)"
echo "========================================="

# 1. 分析老年代回收（Full GC和Old Generation GC）
echo
echo "1. 老年代回收分析"
echo "----------------------------------------"

# 匹配Full GC和Old Generation相关的日志
# 支持多种GC日志格式
grep -E "(Full GC|Old Generation|CMS|G1 Old Generation|Parallel Old)" "$GC_LOG_FILE" | while read line; do
    # 提取时间戳
    timestamp=$(echo "$line" | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+' | head -1)
    if [ -z "$timestamp" ]; then
        # 如果没有完整时间戳，尝试提取相对时间
        timestamp=$(echo "$line" | grep -oE '^[0-9]+\.[0-9]+:' | sed 's/:$//')
    fi
    
    # 提取回收前后的内存大小 (格式: xxxK->yyyK(zzzK))
    memory_info=$(echo "$line" | grep -oE '[0-9]+[KMG]?->[0-9]+[KMG]?\([0-9]+[KMG]?\)')
    
    # 提取停顿时间
    pause_time=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+[ ]*secs?' | head -1)
    
    if [ ! -z "$memory_info" ] && [ ! -z "$pause_time" ]; then
        echo "时间: $timestamp | 内存变化: $memory_info | 停顿时间: $pause_time"
    fi
done

# 2. 分析Stop-The-World超过500ms的情况
echo
echo "2. Stop-The-World超过500ms的时间点"
echo "----------------------------------------"

# 查找停顿时间超过0.5秒的GC事件
grep -E "[0-9]+\.[0-9]+[ ]*secs?" "$GC_LOG_FILE" | while read line; do
    # 提取停顿时间（秒）
    pause_time=$(echo "$line" | grep -oE '[0-9]+\.[0-9]+' | head -1)
    
    if [ ! -z "$pause_time" ]; then
        # 使用awk进行浮点数比较
        is_long_pause=$(echo "$pause_time" | awk '{if($1 > 0.5) print "yes"; else print "no"}')
        
        if [ "$is_long_pause" = "yes" ]; then
            # 提取时间戳
            timestamp=$(echo "$line" | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+' | head -1)
            if [ -z "$timestamp" ]; then
                timestamp=$(echo "$line" | grep -oE '^[0-9]+\.[0-9]+:' | sed 's/:$//')
            fi
            
            # 提取GC类型
            gc_type=$(echo "$line" | grep -oE '(Young|Full|Mixed|G1|CMS|Parallel|Serial)[ ]*GC' | head -1)
            
            echo "时间: $timestamp | GC类型: $gc_type | 停顿时长: ${pause_time}秒"
        fi
    fi
done

# 3. 统计Young GC的频率
echo
echo "3. Young GC频率统计"
echo "----------------------------------------"

# 提取Young GC事件
young_gc_lines=$(grep -E "(Young|Minor)[ ]*GC" "$GC_LOG_FILE")
young_gc_count=$(echo "$young_gc_lines" | wc -l)

if [ $young_gc_count -gt 0 ]; then
    echo "Young GC总次数: $young_gc_count"
    
    # 提取时间戳并计算时间范围
    first_timestamp=$(echo "$young_gc_lines" | head -1 | grep -oE '^[0-9]+\.[0-9]+' | head -1)
    last_timestamp=$(echo "$young_gc_lines" | tail -1 | grep -oE '^[0-9]+\.[0-9]+' | head -1)
    
    if [ ! -z "$first_timestamp" ] && [ ! -z "$last_timestamp" ]; then
        # 计算总时间跨度（秒）
        total_time=$(echo "$last_timestamp $first_timestamp" | awk '{print $1 - $2}')
        
        if [ $(echo "$total_time > 0" | bc -l 2>/dev/null || echo "0") -eq 1 ]; then
            # 计算平均频率（次/秒）
            avg_frequency=$(echo "$young_gc_count $total_time" | awk '{printf "%.4f", $1/$2}')
            echo "时间跨度: ${total_time}秒"
            echo "平均频率: ${avg_frequency}次/秒"
            
            # 分析最高频率（1分钟窗口内的最大GC次数）
            echo
            echo "分析1分钟窗口内的最高频率..."
            
            # 创建临时文件存储时间戳
            temp_file=$(mktemp)
            echo "$young_gc_lines" | grep -oE '^[0-9]+\.[0-9]+' > "$temp_file"
            
            max_frequency=0
            max_frequency_time=""
            
            # 滑动窗口分析
            while read timestamp; do
                if [ ! -z "$timestamp" ]; then
                    # 计算60秒窗口内的GC次数
                    window_end=$(echo "$timestamp + 60" | bc -l)
                    window_count=$(awk -v start="$timestamp" -v end="$window_end" '$1 >= start && $1 <= end {count++} END {print count+0}' "$temp_file")
                    
                    if [ $window_count -gt $max_frequency ]; then
                        max_frequency=$window_count
                        max_frequency_time=$timestamp
                    fi
                fi
            done < "$temp_file"
            
            rm -f "$temp_file"
            
            echo "1分钟内最高频率: ${max_frequency}次 (时间点: ${max_frequency_time}秒)"
        else
            echo "无法计算时间跨度"
        fi
    else
        echo "无法提取时间戳信息"
    fi
else
    echo "未找到Young GC事件"
fi

# 4. 生成汇总统计
echo
echo "4. 汇总统计"
echo "----------------------------------------"

total_gc_count=$(grep -cE "(GC|gc)" "$GC_LOG_FILE")
full_gc_count=$(grep -cE "(Full GC|Full gc)" "$GC_LOG_FILE")
long_pause_count=$(grep -E "[0-9]+\.[0-9]+[ ]*secs?" "$GC_LOG_FILE" | awk '$0 ~ /[0-9]+\.[0-9]+/ {match($0, /[0-9]+\.[0-9]+/); time=substr($0, RSTART, RLENGTH); if(time > 0.5) count++} END {print count+0}')

echo "总GC事件数: $total_gc_count"
echo "Full GC次数: $full_gc_count"
echo "Young GC次数: $young_gc_count"
echo "长时间停顿(>500ms)次数: $long_pause_count"

# 计算GC效率指标
if [ $total_gc_count -gt 0 ]; then
    long_pause_ratio=$(echo "$long_pause_count $total_gc_count" | awk '{printf "%.2f", ($1/$2)*100}')
    echo "长时间停顿占比: ${long_pause_ratio}%"
fi

echo
echo "========================================="
echo "分析完成"
echo "========================================="