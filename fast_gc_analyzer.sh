#!/bin/bash

# 快速GC日志分析脚本 - 优化版本

GC_LOG="$1"

if [ ! -f "$GC_LOG" ]; then
    echo "用法: $0 <GC日志文件>"
    exit 1
fi

echo "快速GC日志分析"
echo "文件: $GC_LOG"
echo "===================="

# 1. 老年代回收分析 - 分析所有Full GC事件
echo
echo "1. 老年代回收分析 (所有Full GC事件)"
echo "--------------------------------"

# 方法1: 从"Heap after GC"行中提取Full GC次数
full_gc_from_heap=$(grep "Heap after GC" "$GC_LOG" | tail -1 | sed -n 's/.*full \([0-9]\+\).*/\1/p')

# 方法2: 直接搜索"Full GC"文本
full_gc_text_count=$(grep -c "Full GC" "$GC_LOG")

# 方法3: 搜索CMS-concurrent-sweep-start等完整的老年代回收周期
cms_sweep_count=$(grep -c "CMS-concurrent-sweep-start" "$GC_LOG")

echo "Full GC统计 (多种方法验证):"
echo "- 从堆信息提取的Full GC次数: ${full_gc_from_heap:-0}"
echo "- 文本搜索'Full GC'的次数: $full_gc_text_count"
echo "- CMS完整回收周期次数: $cms_sweep_count"

# 使用最准确的数字
if [ -n "$full_gc_from_heap" ] && [ "$full_gc_from_heap" -gt 0 ]; then
    full_gc_count=$full_gc_from_heap
    echo "实际Full GC次数: $full_gc_count (基于堆统计信息)"
else
    full_gc_count=$full_gc_text_count
    echo "实际Full GC次数: $full_gc_count (基于文本搜索)"
fi

# 分析Full GC的详细信息
if [ $full_gc_count -gt 0 ]; then
    echo
    echo "Full GC详细分析:"
    
    # 查找显式的Full GC事件
    explicit_full_gc=$(grep "Full GC" "$GC_LOG")
    if [ -n "$explicit_full_gc" ]; then
        echo "显式Full GC事件 (前10个):"
        echo "$explicit_full_gc" | head -10 | while read line; do
            timestamp=$(echo "$line" | sed -n 's/.*\([0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}T[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}\.[0-9]\+\).*/\1/p')
            memory=$(echo "$line" | sed -n 's/.*\([0-9]\+[KMG]\?->[0-9]\+[KMG]\?([0-9]\+[KMG]\?)\).*/\1/p')
            pause=$(echo "$line" | sed -n 's/.*\([0-9]\+\.[0-9]\+\) secs.*/\1/p')
            echo "时间: $timestamp | 内存: $memory | 停顿: ${pause}秒"
        done
    fi
    
    # 分析CMS老年代回收周期
    echo
    echo "CMS老年代回收周期分析:"
    cms_initial_mark=$(grep -c "CMS-initial-mark" "$GC_LOG")
    cms_concurrent_mark=$(grep -c "CMS-concurrent-mark-start" "$GC_LOG")
    cms_remark=$(grep -c "CMS-remark" "$GC_LOG")
    cms_concurrent_sweep=$(grep -c "CMS-concurrent-sweep-start" "$GC_LOG")
    
    echo "- CMS初始标记: $cms_initial_mark 次"
    echo "- CMS并发标记: $cms_concurrent_mark 次"
    echo "- CMS重新标记: $cms_remark 次"
    echo "- CMS并发清除: $cms_concurrent_sweep 次"
    
    # 显示最近的CMS事件示例
    echo
    echo "最近的CMS事件示例 (前5个):"
    grep -E "CMS-initial-mark|CMS-remark|CMS-concurrent-sweep-start" "$GC_LOG" | head -5 | while read line; do
        timestamp=$(echo "$line" | sed -n 's/.*\([0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}T[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}\.[0-9]\+\).*/\1/p')
        cms_phase=$(echo "$line" | sed -n 's/.*\(CMS[^,)]*\).*/\1/p')
        pause=$(echo "$line" | sed -n 's/.*\([0-9]\+\.[0-9]\+\) secs.*/\1/p')
        echo "时间: $timestamp | CMS阶段: $cms_phase | 停顿: ${pause}秒"
    done
else
    echo "未发现Full GC事件"
fi

# 2. 长停顿分析 - 查找所有超过500ms的停顿
echo
echo "2. 超过500ms的停顿 (所有事件)"
echo "--------------------------------"

long_pause_events=$(grep "Total time for which application threads were stopped" "$GC_LOG" | \
awk -F'stopped: ' '{
    if ($2 && $2+0 > 0.5) {
        match($0, /[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+/);
        timestamp = substr($0, RSTART, RLENGTH);
        match($2, /[0-9]+\.[0-9]+/);
        pause = substr($2, RSTART, RLENGTH);
        print "时间: " timestamp " | 停顿: " pause "秒";
    }
}')

long_pause_count=$(echo "$long_pause_events" | wc -l)
echo "长停顿事件总数: $long_pause_count"

if [ $long_pause_count -gt 0 ]; then
    echo "所有长停顿事件:"
    echo "$long_pause_events"
    
    # 计算长停顿的统计信息
    echo
    echo "长停顿统计分析:"
    echo "$long_pause_events" | awk -F'停顿: ' '{
        if ($2) {
            pause = $2+0;
            sum += pause;
            if (pause > max) max = pause;
            if (min == 0 || pause < min) min = pause;
            count++;
        }
    } END {
        if (count > 0) {
            printf "平均停顿时间: %.4f秒\n", sum/count;
            printf "最大停顿时间: %.4f秒\n", max;
            printf "最小停顿时间: %.4f秒\n", min;
        }
    }'
else
    echo "未发现超过500ms的停顿事件"
fi

# 3. Young GC频率统计
echo
echo "3. Young GC频率统计"
echo "--------------------------------"

young_count=$(grep -c "ParNew" "$GC_LOG")
echo "Young GC总次数: $young_count"

if [ $young_count -gt 0 ]; then
    # 获取第一个和最后一个时间戳
    first_time=$(grep "ParNew" "$GC_LOG" | head -1 | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p')
    last_time=$(grep "ParNew" "$GC_LOG" | tail -1 | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p')
    
    if [ -n "$first_time" ] && [ -n "$last_time" ]; then
        echo "时间范围: ${first_time}秒 到 ${last_time}秒"
        
        # 使用awk计算频率
        echo "$young_count $first_time $last_time" | awk '{
            total_time = $3 - $2;
            if (total_time > 0) {
                avg_freq = $1 / total_time;
                printf "平均频率: %.4f次/秒\n", avg_freq;
                printf "时间跨度: %.2f秒\n", total_time;
            }
        }'
        
        # 最高频率分析 - 使用采样方式分析大数据集
        echo "分析最高频率(智能采样分析)..."
        
        # 对于大数据集，使用采样方式避免性能问题
        sample_size=1000
        if [ $young_count -gt $sample_size ]; then
            echo "数据量较大，使用采样分析(采样$sample_size个事件)..."
            grep "ParNew" "$GC_LOG" | head -$sample_size | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p'
        else
            echo "分析所有Young GC事件..."
            grep "ParNew" "$GC_LOG" | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p'
        fi | awk '{
            times[NR] = $1;
            count = NR;
        }
        END {
            max_freq = 0;
            max_time = 0;
            # 使用步长优化大数据集分析
            step = (count > 500) ? int(count/100) : 1;
            
            for (i = 1; i <= count; i += step) {
                window_count = 0;
                for (j = 1; j <= count; j++) {
                    if (times[j] >= times[i] && times[j] <= times[i] + 60) {
                        window_count++;
                    }
                }
                if (window_count > max_freq) {
                    max_freq = window_count;
                    max_time = times[i];
                }
            }
            printf "1分钟内最高频率: %d次 (时间点: %.3f秒)\n", max_freq, max_time;
        }'
    fi
fi

# 4. 快速统计
echo
echo "4. 快速统计"
echo "--------------------------------"

total_gc=$(grep -c "#[0-9]\+:" "$GC_LOG")
full_gc=$(grep -c "Full GC" "$GC_LOG")

# 快速计算长停顿数量
long_pause=$(grep "Total time for which application threads were stopped" "$GC_LOG" | \
awk -F'stopped: ' '{if ($2+0 > 0.5) count++} END {print count+0}')

echo "总GC事件: $total_gc"
echo "Young GC: $young_count"  
echo "Full GC: $full_gc_count"
echo "长停顿(>500ms): $long_pause"

# 计算GC效率指标
if [ $total_gc -gt 0 ]; then
    young_gc_ratio=$(echo "scale=2; $young_count * 100 / $total_gc" | bc -l 2>/dev/null || echo "0")
    full_gc_ratio=$(echo "scale=2; $full_gc_count * 100 / $total_gc" | bc -l 2>/dev/null || echo "0")
    echo "Young GC占比: ${young_gc_ratio}%"
    echo "Full GC占比: ${full_gc_ratio}%"
fi

echo
echo "===================="
echo "分析完成"