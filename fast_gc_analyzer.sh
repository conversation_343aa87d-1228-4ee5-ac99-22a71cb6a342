#!/bin/bash

# Java GC日志分析脚本 - 专业版本
# 分析Full GC和长停顿事件

GC_LOG="$1"

if [ ! -f "$GC_LOG" ]; then
    echo "用法: $0 <GC日志文件>"
    echo "功能: 分析Java GC日志，提取Full GC和长停顿信息"
    exit 1
fi

echo "Java GC日志分析"
echo "文件: $GC_LOG"
echo "===================="

# 1. Full GC详细分析
echo
echo "1. Full GC详细分析"
echo "--------------------------------"

# 分析Full GC事件，提取时间、内存信息
analyze_full_gc() {
    local gc_log="$1"

    # 查找Full GC事件，支持多种GC日志格式
    # 格式1: [Full GC (Allocation Failure) 1234K->567K(2048K), 0.123 secs]
    # 格式2: 2023-01-01T12:00:00.123+0800: [Full GC ...]
    # 格式3: [GC (Allocation Failure) [PSYoungGen: ...] [ParOldGen: ...] 1234K->567K(2048K), 0.123 secs]
    # 格式4: CMS格式 - 通过full计数器变化检测Full GC

    echo "Full GC事件列表:"
    echo "时间 | 回收前内存 | 回收后内存 | 总堆大小 | 停顿时间"
    echo "------------------------------------------------------------"

    local full_gc_count=0

    # 方法1: 处理显式的Full GC事件
    grep -E "(Full GC|PSYoungGen.*ParOldGen)" "$gc_log" | while IFS= read -r line; do
        # 提取时间戳
        timestamp=""
        if [[ $line =~ ([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+) ]]; then
            timestamp="${BASH_REMATCH[1]}"
        elif [[ $line =~ ([0-9]+\.[0-9]+): ]]; then
            # JVM启动后的秒数
            timestamp="${BASH_REMATCH[1]}s"
        fi

        # 提取内存信息 (格式: 1234K->567K(2048K) 或类似)
        memory_before=""
        memory_after=""
        heap_total=""

        # 查找堆内存信息，避免Metaspace信息
        # 移除所有方括号内的内容，只保留最后的堆总体信息
        temp_line="$line"
        # 移除所有[...]部分，保留最后的堆信息
        temp_line=$(echo "$temp_line" | sed 's/\[[^]]*\]//g')

        # 查找内存信息模式（现在应该是整个堆的信息）
        if [[ $temp_line =~ ([0-9]+[KMG]?)-\>([0-9]+[KMG]?)\(([0-9]+[KMG]?)\) ]]; then
            memory_before="${BASH_REMATCH[1]}"
            memory_after="${BASH_REMATCH[2]}"
            heap_total="${BASH_REMATCH[3]}"
        fi

        # 提取停顿时间
        pause_time=""
        if [[ $line =~ ([0-9]+\.[0-9]+)\ secs ]]; then
            pause_time="${BASH_REMATCH[1]}s"
        fi

        # 输出结果
        if [[ -n "$timestamp" && -n "$memory_before" && -n "$memory_after" ]]; then
            printf "%-20s | %-12s | %-12s | %-10s | %s\n" \
                "$timestamp" "$memory_before" "$memory_after" "$heap_total" "$pause_time"
            ((full_gc_count++))
        fi
    done

    # 方法2: 检测CMS格式的Full GC (通过full计数器变化)
    echo
    echo "检测CMS格式的Full GC事件..."

    # 分析CMS Full GC事件
    analyze_cms_full_gc "$gc_log"

    # 统计Full GC次数
    local total_full_gc=$(grep -c -E "(Full GC|PSYoungGen.*ParOldGen)" "$gc_log")
    local cms_full_gc=$(analyze_cms_full_gc_count "$gc_log")

    echo
    echo "Full GC统计:"
    echo "- 显式Full GC次数: $total_full_gc"
    echo "- CMS Full GC次数: $cms_full_gc"
    echo "- 总计Full GC次数: $((total_full_gc + cms_full_gc))"

    # 分析Full GC触发原因
    echo
    echo "Full GC触发原因分析:"
    grep -o -E "Full GC \([^)]+\)" "$gc_log" | sort | uniq -c | sort -nr | while read count reason; do
        echo "- $reason: $count 次"
    done
}

# 分析CMS格式的Full GC事件
analyze_cms_full_gc() {
    local gc_log="$1"

    # 使用awk分析CMS Full GC事件
    awk '
    BEGIN {
        prev_full = -1
        in_full_gc = 0
        before_time = ""
        before_memory = ""
        before_total = ""
        after_memory = ""
        after_total = ""
    }

    # 匹配Heap before GC行
    /Heap before GC invocations=/ {
        # 提取full计数器
        match($0, /\(full ([0-9]+)\)/, arr)
        current_full = arr[1]

        # 提取时间戳
        match($0, /([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+)/, time_arr)
        timestamp = time_arr[1]

        if (prev_full != -1 && current_full > prev_full) {
            # 检测到Full GC开始
            in_full_gc = 1
            before_time = timestamp
        }
        prev_full = current_full
    }

    # 在Full GC期间，收集内存信息
    in_full_gc && /par new generation.*total ([0-9]+K), used ([0-9]+K)/ {
        match($0, /total ([0-9]+K), used ([0-9]+K)/, mem_arr)
        young_total = mem_arr[1]
        young_used = mem_arr[2]
    }

    in_full_gc && /concurrent mark-sweep generation.*total ([0-9]+K), used ([0-9]+K)/ {
        match($0, /total ([0-9]+K), used ([0-9]+K)/, mem_arr)
        old_total = mem_arr[1]
        old_used = mem_arr[2]

        # 计算总内存
        young_total_num = young_total; gsub(/K/, "", young_total_num)
        old_total_num = old_total; gsub(/K/, "", old_total_num)
        total_heap = (young_total_num + old_total_num) "K"

        young_used_num = young_used; gsub(/K/, "", young_used_num)
        old_used_num = old_used; gsub(/K/, "", old_used_num)
        total_used = (young_used_num + old_used_num) "K"

        if (before_memory == "") {
            before_memory = total_used
            before_total = total_heap
        } else {
            after_memory = total_used
            after_total = total_heap
        }
    }

    # 匹配Heap after GC行，结束Full GC分析
    /Heap after GC invocations=/ && in_full_gc {
        in_full_gc = 0

        # 输出Full GC信息
        if (before_time != "" && before_memory != "" && after_memory != "") {
            printf "%-20s | %-12s | %-12s | %-10s | CMS Full GC\n", before_time, before_memory, after_memory, before_total
        }

        # 重置变量
        before_time = ""
        before_memory = ""
        before_total = ""
        after_memory = ""
        after_total = ""
    }
    ' "$gc_log"
}

# 统计CMS Full GC次数
analyze_cms_full_gc_count() {
    local gc_log="$1"

    # 通过full计数器变化统计Full GC次数
    grep -E "Heap before GC invocations=.*\(full [0-9]+\)" "$gc_log" | \
    awk '
    BEGIN { prev_full = -1; count = 0 }
    {
        match($0, /\(full ([0-9]+)\)/, arr)
        current_full = arr[1]
        if (prev_full != -1 && current_full > prev_full) {
            count++
        }
        prev_full = current_full
    }
    END { print count }
    '
}

analyze_full_gc "$GC_LOG"

# 2. 长停顿分析 - 查找所有超过500ms的停顿
echo
echo "2. 应用程序停顿分析 (>500ms)"
echo "--------------------------------"

analyze_long_pauses() {
    local gc_log="$1"
    local threshold=0.5  # 500ms阈值

    echo "应用程序停顿事件 (超过500ms):"
    echo "时间 | 停顿时长 | 停顿类型"
    echo "----------------------------------------"

    local pause_count=0
    local total_pause_time=0
    local max_pause=0
    local min_pause=999999

    # 方法1: 分析"Total time for which application threads were stopped"
    if grep -q "Total time for which application threads were stopped" "$gc_log"; then
        grep "Total time for which application threads were stopped" "$gc_log" | while IFS= read -r line; do
            # 提取时间戳
            timestamp=""
            if [[ $line =~ ([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+) ]]; then
                timestamp="${BASH_REMATCH[1]}"
            elif [[ $line =~ ([0-9]+\.[0-9]+): ]]; then
                timestamp="${BASH_REMATCH[1]}s"
            fi

            # 提取停顿时间
            if [[ $line =~ stopped:[[:space:]]*([0-9]+\.[0-9]+) ]]; then
                pause_time="${BASH_REMATCH[1]}"

                # 检查是否超过阈值
                if (( $(echo "$pause_time > $threshold" | bc -l) )); then
                    printf "%-20s | %-10s | %s\n" "$timestamp" "${pause_time}s" "应用线程停顿"
                    ((pause_count++))
                fi
            fi
        done
    fi

    # 方法2: 分析GC事件中的停顿时间
    grep -E "\[[0-9]+\.[0-9]+ secs\]|\, [0-9]+\.[0-9]+ secs\]" "$gc_log" | while IFS= read -r line; do
        # 提取时间戳
        timestamp=""
        if [[ $line =~ ([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+) ]]; then
            timestamp="${BASH_REMATCH[1]}"
        elif [[ $line =~ ([0-9]+\.[0-9]+): ]]; then
            timestamp="${BASH_REMATCH[1]}s"
        fi

        # 提取停顿时间
        if [[ $line =~ ([0-9]+\.[0-9]+)\ secs ]]; then
            pause_time="${BASH_REMATCH[1]}"

            # 检查是否超过阈值
            if (( $(echo "$pause_time > $threshold" | bc -l) )); then
                # 确定GC类型
                gc_type="GC停顿"
                if [[ $line =~ "Full GC" ]]; then
                    gc_type="Full GC停顿"
                elif [[ $line =~ "ParNew" ]]; then
                    gc_type="Young GC停顿"
                elif [[ $line =~ "CMS" ]]; then
                    gc_type="CMS停顿"
                fi

                printf "%-20s | %-10s | %s\n" "$timestamp" "${pause_time}s" "$gc_type"
            fi
        fi
    done | sort -u  # 去重，避免重复显示同一事件

    # 统计信息
    echo
    echo "长停顿统计信息:"

    # 重新计算统计数据
    local stats=$(grep -E "(Total time for which application threads were stopped|[0-9]+\.[0-9]+ secs)" "$gc_log" | \
    awk -v threshold="$threshold" '
    BEGIN { count=0; sum=0; max=0; min=999999 }
    {
        if (match($0, /stopped:[[:space:]]*([0-9]+\.[0-9]+)/, arr)) {
            pause = arr[1] + 0;
        } else if (match($0, /([0-9]+\.[0-9]+) secs/, arr)) {
            pause = arr[1] + 0;
        } else {
            next;
        }

        if (pause > threshold) {
            count++;
            sum += pause;
            if (pause > max) max = pause;
            if (pause < min) min = pause;
        }
    }
    END {
        if (count > 0) {
            printf "- 长停顿事件总数: %d\n", count;
            printf "- 平均停顿时间: %.4f秒\n", sum/count;
            printf "- 最大停顿时间: %.4f秒\n", max;
            printf "- 最小停顿时间: %.4f秒\n", min;
            printf "- 总停顿时间: %.4f秒\n", sum;
        } else {
            print "- 未发现超过500ms的停顿事件";
        }
    }')

    echo "$stats"
}

analyze_long_pauses "$GC_LOG"

# 3. 总体统计信息
echo
echo "3. GC总体统计"
echo "--------------------------------"

# 统计各种GC事件
total_gc=$(grep -c -E "(GC|Full GC)" "$GC_LOG")
young_gc=$(grep -c -E "(ParNew|PSYoungGen)" "$GC_LOG")
full_gc=$(grep -c -E "(Full GC|PSYoungGen.*ParOldGen)" "$GC_LOG")
cms_gc=$(grep -c "CMS" "$GC_LOG")

# 统计长停顿事件
long_pause_count=$(grep -E "(Total time for which application threads were stopped|[0-9]+\.[0-9]+ secs)" "$GC_LOG" | \
awk 'BEGIN { count=0 }
{
    if (match($0, /stopped:[[:space:]]*([0-9]+\.[0-9]+)/, arr)) {
        pause = arr[1] + 0;
    } else if (match($0, /([0-9]+\.[0-9]+) secs/, arr)) {
        pause = arr[1] + 0;
    } else {
        next;
    }
    if (pause > 0.5) count++;
}
END { print count }')

echo "GC事件统计:"
echo "- 总GC事件数: $total_gc"
echo "- Young GC: $young_gc"
echo "- Full GC: $full_gc"
echo "- CMS相关事件: $cms_gc"
echo "- 长停顿事件(>500ms): $long_pause_count"

# 计算比例
if [ $total_gc -gt 0 ]; then
    young_ratio=$(echo "scale=1; $young_gc * 100 / $total_gc" | bc -l 2>/dev/null || echo "0")
    full_ratio=$(echo "scale=1; $full_gc * 100 / $total_gc" | bc -l 2>/dev/null || echo "0")
    echo
    echo "GC类型分布:"
    echo "- Young GC占比: ${young_ratio}%"
    echo "- Full GC占比: ${full_ratio}%"
fi

# 分析时间范围
echo
echo "日志时间范围分析:"
first_timestamp=$(grep -o -E "[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+" "$GC_LOG" | head -1)
last_timestamp=$(grep -o -E "[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+" "$GC_LOG" | tail -1)

if [ -n "$first_timestamp" ] && [ -n "$last_timestamp" ]; then
    echo "- 开始时间: $first_timestamp"
    echo "- 结束时间: $last_timestamp"
else
    # 尝试提取JVM启动后的时间
    first_time=$(grep -o -E "[0-9]+\.[0-9]+:" "$GC_LOG" | head -1 | sed 's/://')
    last_time=$(grep -o -E "[0-9]+\.[0-9]+:" "$GC_LOG" | tail -1 | sed 's/://')
    if [ -n "$first_time" ] && [ -n "$last_time" ]; then
        echo "- 开始时间: ${first_time}s (JVM启动后)"
        echo "- 结束时间: ${last_time}s (JVM启动后)"
        duration=$(echo "scale=2; $last_time - $first_time" | bc -l 2>/dev/null || echo "未知")
        echo "- 日志时长: ${duration}s"
    fi
fi

echo
echo "===================="
echo "Java GC日志分析完成"
echo
echo "说明:"
echo "- Full GC: 显示每次Full GC的时间、回收前后内存大小"
echo "- 长停顿: 显示所有超过500ms的应用程序停顿事件"
echo "- 统计信息: 提供GC事件的总体概览"