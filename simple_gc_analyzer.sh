#!/bin/bash

# 简洁的Java GC日志分析脚本
# 专注于三个核心分析：老年代回收、长停顿、Young GC频率

if [ $# -eq 0 ]; then
    echo "用法: $0 <GC日志文件>"
    exit 1
fi

GC_LOG="$1"

if [ ! -f "$GC_LOG" ]; then
    echo "错误: 文件不存在"
    exit 1
fi

echo "Java GC日志分析报告"
echo "文件: $GC_LOG"
echo "时间: $(date)"
echo "=================================="

# 1. 老年代回收分析
echo
echo "1. 老年代回收时间和大小变化"
echo "--------------------------------"

# 查找Full GC事件，提取时间、内存变化和停顿时间
grep "Full GC" "$GC_LOG" | head -10 | while IFS= read -r line; do
    # 提取时间戳
    timestamp=$(echo "$line" | sed -n 's/.*\([0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}T[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}\.[0-9]\+\).*/\1/p')
    
    # 提取内存变化 (格式: xxxK->yyyK(zzzK))
    memory=$(echo "$line" | sed -n 's/.*\([0-9]\+[KMG]\?->[0-9]\+[KMG]\?([0-9]\+[KMG]\?)\).*/\1/p')
    
    # 提取停顿时间
    pause=$(echo "$line" | sed -n 's/.*\([0-9]\+\.[0-9]\+\) secs.*/\1/p')
    
    if [ -n "$timestamp" ] && [ -n "$memory" ] && [ -n "$pause" ]; then
        echo "$timestamp | 内存: $memory | 停顿: ${pause}秒"
    fi
done

# 2. 超过500ms的停顿分析
echo
echo "2. Stop-The-World超过500ms的时间点"
echo "--------------------------------"

# 查找应用停顿时间超过0.5秒的事件
grep "Total time for which application threads were stopped" "$GC_LOG" | while IFS= read -r line; do
    # 提取停顿时间
    pause_time=$(echo "$line" | sed -n 's/.*stopped: \([0-9]\+\.[0-9]\+\) seconds.*/\1/p')
    
    if [ -n "$pause_time" ]; then
        # 检查是否超过0.5秒 (使用awk进行浮点比较)
        is_long=$(echo "$pause_time" | awk '{if($1 > 0.5) print "1"; else print "0"}')
        
        if [ "$is_long" = "1" ]; then
            # 提取时间戳
            timestamp=$(echo "$line" | sed -n 's/.*\([0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}T[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}\.[0-9]\+\).*/\1/p')
            echo "$timestamp | 停顿时长: ${pause_time}秒"
        fi
    fi
done

# 3. Young GC频率统计
echo
echo "3. Young GC频率统计"
echo "--------------------------------"

# 统计ParNew (Young GC)事件
young_gc_count=$(grep -c "ParNew" "$GC_LOG")
echo "Young GC总次数: $young_gc_count"

if [ $young_gc_count -gt 0 ]; then
    # 提取第一个和最后一个Young GC的时间戳
    first_time=$(grep "ParNew" "$GC_LOG" | head -1 | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p')
    last_time=$(grep "ParNew" "$GC_LOG" | tail -1 | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p')
    
    if [ -n "$first_time" ] && [ -n "$last_time" ]; then
        # 计算时间跨度和平均频率
        total_time=$(echo "$last_time - $first_time" | bc -l 2>/dev/null || echo "0")
        
        if [ $(echo "$total_time > 0" | bc -l 2>/dev/null || echo "0") -eq 1 ]; then
            avg_freq=$(echo "scale=4; $young_gc_count / $total_time" | bc -l 2>/dev/null || echo "0")
            echo "时间跨度: ${total_time}秒"
            echo "平均频率: ${avg_freq}次/秒"
            
            # 分析最高频率 - 简化版本：查找连续60秒内的最大GC次数
            echo "正在分析1分钟窗口内的最高频率..."
            
            # 提取所有Young GC时间戳
            temp_times=$(mktemp)
            grep "ParNew" "$GC_LOG" | sed -n 's/.*: \([0-9]\+\.[0-9]\+\):.*/\1/p' > "$temp_times"
            
            max_count=0
            max_time=""
            
            # 简化的滑动窗口分析
            while read -r time; do
                if [ -n "$time" ]; then
                    # 计算60秒窗口内的GC次数
                    window_end=$(echo "$time + 60" | bc -l 2>/dev/null || echo "$time")
                    count=$(awk -v start="$time" -v end="$window_end" '$1 >= start && $1 <= end {c++} END {print c+0}' "$temp_times")
                    
                    if [ $count -gt $max_count ]; then
                        max_count=$count
                        max_time=$time
                    fi
                fi
            done < "$temp_times"
            
            rm -f "$temp_times"
            echo "1分钟内最高频率: ${max_count}次 (时间点: ${max_time}秒)"
        fi
    fi
fi

# 4. 汇总统计
echo
echo "4. 汇总统计"
echo "--------------------------------"

total_gc=$(grep -c "#[0-9]\+:" "$GC_LOG")
full_gc=$(grep -c "Full GC" "$GC_LOG")
long_pause=$(grep "Total time for which application threads were stopped" "$GC_LOG" | awk -F'stopped: ' '{print $2}' | awk '{if($1 > 0.5) count++} END {print count+0}')

echo "总GC事件: $total_gc"
echo "Young GC: $young_gc_count"
echo "Full GC: $full_gc"
echo "长停顿(>500ms): $long_pause"

echo
echo "=================================="
echo "分析完成"